import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

// Client للاستخدام في المتصفح
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);

// أنواع البيانات
export interface Database {
  public: {
    Tables: {
      categories: {
        Row: {
          id: string;
          name: string;
          slug: string;
          description: string | null;
          color: string;
          icon: string | null;
          order_index: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          slug: string;
          description?: string | null;
          color?: string;
          icon?: string | null;
          order_index?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          slug?: string;
          description?: string | null;
          color?: string;
          icon?: string | null;
          order_index?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      articles: {
        Row: {
          id: string;
          title: string;
          slug: string;
          description: string;
          content: string;
          category_id: string | null;
          category_slug: string | null;
          tags: string[];
          author: string;
          author_id: string | null;
          read_time: string;
          status: 'draft' | 'published' | 'archived';
          featured: boolean;
          views: number;
          likes: number;
          published_at: string | null;
          created_at: string;
          updated_at: string;
          seo_title: string | null;
          seo_description: string | null;
          seo_keywords: string[];
          related_tools: string[];
          related_articles: string[];
          image_url: string | null;
          image_alt: string | null;
        };
        Insert: {
          id?: string;
          title: string;
          slug: string;
          description: string;
          content: string;
          category_id?: string | null;
          category_slug?: string | null;
          tags?: string[];
          author?: string;
          author_id?: string | null;
          read_time?: string;
          status?: 'draft' | 'published' | 'archived';
          featured?: boolean;
          views?: number;
          likes?: number;
          published_at?: string | null;
          created_at?: string;
          updated_at?: string;
          seo_title?: string | null;
          seo_description?: string | null;
          seo_keywords?: string[];
          related_tools?: string[];
          related_articles?: string[];
          image_url?: string | null;
          image_alt?: string | null;
        };
        Update: {
          id?: string;
          title?: string;
          slug?: string;
          description?: string;
          content?: string;
          category_id?: string | null;
          category_slug?: string | null;
          tags?: string[];
          author?: string;
          author_id?: string | null;
          read_time?: string;
          status?: 'draft' | 'published' | 'archived';
          featured?: boolean;
          views?: number;
          likes?: number;
          published_at?: string | null;
          created_at?: string;
          updated_at?: string;
          seo_title?: string | null;
          seo_description?: string | null;
          seo_keywords?: string[];
          related_tools?: string[];
          related_articles?: string[];
          image_url?: string | null;
          image_alt?: string | null;
        };
      };
    };
  };
}

// Helper functions
export const generateSlug = (text: string): string => {
  return text
    .toLowerCase()
    .replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFFa-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
};

export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('ar-SA-u-nu-latn');
};

export const formatRelativeTime = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  if (diffInSeconds < 60) {
    return 'منذ لحظات';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `منذ ${minutes} دقيقة`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `منذ ${hours} ساعة`;
  } else if (diffInSeconds < 2592000) {
    const days = Math.floor(diffInSeconds / 86400);
    return `منذ ${days} يوم`;
  } else if (diffInSeconds < 31536000) {
    const months = Math.floor(diffInSeconds / 2592000);
    return `منذ ${months} شهر`;
  } else {
    const years = Math.floor(diffInSeconds / 31536000);
    return `منذ ${years} سنة`;
  }
};
