'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  UserPlus, 
  X,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { createUser } from '@/lib/auth-supabase';

interface AddUserFormProps {
  onClose: () => void;
  onUserAdded: () => void;
  onUserCreated?: (user: any, password: string) => void;
}

export default function AddUserForm({ onClose, onUserAdded, onUserCreated }: AddUserFormProps) {
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    fullName: '',
    password: '',
    confirmPassword: '',
    role: 'editor' as 'admin' | 'editor'
  });
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // التحقق من البيانات
    if (!formData.username || !formData.fullName || !formData.password) {
      setMessage({ type: 'error', text: 'يرجى ملء جميع الحقول المطلوبة' });
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      setMessage({ type: 'error', text: 'كلمة المرور وتأكيدها غير متطابقين' });
      return;
    }

    if (formData.password.length < 6) {
      setMessage({ type: 'error', text: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل' });
      return;
    }

    setLoading(true);
    setMessage(null);

    try {
      const newUser = await createUser({
        username: formData.username,
        email: formData.email || `${formData.username}@jami3eladawat.com`,
        fullName: formData.fullName,
        password: formData.password,
        role: formData.role
      });

      if (newUser) {
        setMessage({
          type: 'success',
          text: 'تم إنشاء المستخدم بنجاح! ستظهر كلمة المرور في الجدول لمدة 5 دقائق فقط.'
        });

        // إرسال بيانات المستخدم وكلمة المرور للمكون الأب
        if (onUserCreated) {
          onUserCreated(newUser, formData.password);
        }

        setTimeout(() => {
          onUserAdded();
          onClose();
        }, 2500); // زيادة الوقت قليلاً لقراءة الرسالة
      } else {
        setMessage({ type: 'error', text: 'فشل في إنشاء المستخدم لسبب غير معروف' });
      }
    } catch (error) {
      console.error('Error creating user:', error);
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ أثناء إنشاء المستخدم';
      setMessage({ type: 'error', text: errorMessage });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setMessage(null); // إخفاء الرسائل عند التعديل
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-md max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2 space-x-reverse">
              <UserPlus className="h-5 w-5" />
              <span>إضافة مستخدم جديد</span>
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* اسم المستخدم */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                اسم المستخدم *
              </label>
              <input
                type="text"
                value={formData.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="أدخل اسم المستخدم"
                required
              />
            </div>

            {/* البريد الإلكتروني */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                البريد الإلكتروني
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="أدخل البريد الإلكتروني (اختياري)"
              />
            </div>

            {/* الاسم الكامل */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                الاسم الكامل *
              </label>
              <input
                type="text"
                value={formData.fullName}
                onChange={(e) => handleInputChange('fullName', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="أدخل الاسم الكامل"
                required
              />
            </div>

            {/* الدور */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                الدور *
              </label>
              <select
                value={formData.role}
                onChange={(e) => handleInputChange('role', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
                <option value="editor">محرر المقالات</option>
                <option value="admin">مدير النظام</option>
              </select>
            </div>

            {/* كلمة المرور */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                كلمة المرور *
              </label>
              <input
                type="password"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="أدخل كلمة المرور"
                required
                minLength={6}
              />
            </div>

            {/* تأكيد كلمة المرور */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                تأكيد كلمة المرور *
              </label>
              <input
                type="password"
                value={formData.confirmPassword}
                onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="أعد إدخال كلمة المرور"
                required
                minLength={6}
              />
            </div>

            {/* رسالة النتيجة */}
            {message && (
              <div className={`p-3 rounded-lg flex items-center space-x-2 space-x-reverse ${
                message.type === 'success' 
                  ? 'bg-green-50 border border-green-200 text-green-800' 
                  : 'bg-red-50 border border-red-200 text-red-800'
              }`}>
                {message.type === 'success' ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  <AlertCircle className="h-4 w-4" />
                )}
                <span className="text-sm">{message.text}</span>
              </div>
            )}

            {/* أزرار الإجراءات */}
            <div className="flex items-center space-x-2 space-x-reverse pt-4">
              <Button
                type="submit"
                disabled={loading}
                className="flex items-center space-x-2 space-x-reverse"
              >
                <UserPlus className="h-4 w-4" />
                <span>{loading ? 'جاري الإنشاء...' : 'إنشاء المستخدم'}</span>
              </Button>
              
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={loading}
              >
                إلغاء
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
